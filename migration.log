PS H:\cisco-fmc> python.exe .\fmc_migration_v2.py .\fmc_migration_config.json --overwrite
[OK] Using fmcapi library for FMC connectivity
[OK] fmcapi connection created for https://*************
2025-08-04 05:33:42,067 | INFO | ================================================================================
INFO:fmc_migration_v2:================================================================================
2025-08-04 05:33:42,069 | INFO | FMC Migration Engine v2.0 Started
INFO:fmc_migration_v2:FMC Migration Engine v2.0 Started
2025-08-04 05:33:42,071 | INFO | Session ID: migration_1754300022
INFO:fmc_migration_v2:Session ID: migration_1754300022
2025-08-04 05:33:42,073 | INFO | Connection Type: fmcapi
INFO:fmc_migration_v2:Connection Type: fmcapi
2025-08-04 05:33:42,074 | INFO | 🔍 Connection Diagnostic:
INFO:fmc_migration_v2:🔍 Connection Diagnostic:
2025-08-04 05:33:42,076 | INFO |    • FMC Object Type: <class 'fmcapi.fmc.FMC'>
INFO:fmc_migration_v2:   • FMC Object Type: <class 'fmcapi.fmc.FMC'>
2025-08-04 05:33:42,077 | INFO |    • fmcapi Available: True
INFO:fmc_migration_v2:   • fmcapi Available: True
2025-08-04 05:33:42,079 | INFO |    • Available Methods: __enter__ (context manager)
INFO:fmc_migration_v2:   • Available Methods: __enter__ (context manager)
INFO:root:Requesting new tokens from https://*************/api/fmc_platform/v1/auth/generatetoken.
INFO:root:Building base to URLs.
INFO:root:Populating vdbVersion, sruVersion, serverVersion, and geoVersion FMC instance variables.
INFO:root:This FMC's version is 7.4.2 (build 172)
2025-08-04 05:33:44,117 | INFO |    • fmcapi Hosts object created successfully
INFO:fmc_migration_v2:   • fmcapi Hosts object created successfully
2025-08-04 05:33:44,126 | INFO |    • fmcapi Hosts methods: ['FILTER_BY_NAME', 'FIRST_SUPPORTED_FMC_VERSION', 'GLOBAL_VALID_FOR_KWARGS', 'REQUIRED_FOR_BULK_DELETE', 'REQUIRED_FOR_BULK_POST', 'REQUIRED_FOR_DELETE', 'REQUIRED_FOR_GET', 'REQUIRED_FOR_POST', 'REQUIRED_FOR_PUT', 'REQUIRED_GET_FILTERS']...
INFO:fmc_migration_v2:   • fmcapi Hosts methods: ['FILTER_BY_NAME', 'FIRST_SUPPORTED_FMC_VERSION', 'GLOBAL_VALID_FOR_KWARGS', 'REQUIRED_FOR_BULK_DELETE', 'REQUIRED_FOR_BULK_POST', 'REQUIRED_FOR_DELETE', 'REQUIRED_FOR_GET', 'REQUIRED_FOR_POST', 'REQUIRED_FOR_PUT', 'REQUIRED_GET_FILTERS']...
INFO:root:Auto deploy changes set to False.  Use the Deploy button in FMC to push changes to FTDs.
2025-08-04 05:33:44,130 | INFO | 🔍 Connection diagnostic complete
INFO:fmc_migration_v2:🔍 Connection diagnostic complete
2025-08-04 05:33:44,132 | INFO | ================================================================================
INFO:fmc_migration_v2:================================================================================
2025-08-04 05:33:44,137 | INFO | [LOAD] Loading migration configuration: .\fmc_migration_config.json
INFO:fmc_migration_v2:[LOAD] Loading migration configuration: .\fmc_migration_config.json
2025-08-04 05:33:44,156 | INFO | [DOC] Detected v1.0 config format (api_calls structure)
INFO:fmc_migration_v2:[DOC] Detected v1.0 config format (api_calls structure)
2025-08-04 05:33:44,159 | INFO | [INFO] Found 629 host objects in v1.0 format
INFO:fmc_migration_v2:[INFO] Found 629 host objects in v1.0 format
2025-08-04 05:33:44,162 | INFO | [INFO] Found 63 network objects in v1.0 format
INFO:fmc_migration_v2:[INFO] Found 63 network objects in v1.0 format
2025-08-04 05:33:44,165 | INFO | [INFO] Found 29 service objects in v1.0 format
INFO:fmc_migration_v2:[INFO] Found 29 service objects in v1.0 format
2025-08-04 05:33:44,168 | INFO | [START] Starting Host Objects migration...
INFO:fmc_migration_v2:[START] Starting Host Objects migration...
2025-08-04 05:33:44,171 | INFO | [INFO] Processing 629 hosts objects...
INFO:fmc_migration_v2:[INFO] Processing 629 hosts objects...
DEBUG:fmc_migration_v2:Processing hosts 1: RadSaratoga
DEBUG:fmc_migration_v2:Object data keys: ['name', 'type', 'value', 'description', 'overridable']
DEBUG:fmc_migration_v2:Object data: {'name': 'RadSaratoga', 'type': 'Host', 'value': '***********', 'description': 'Migrated from ASA - ', 'overridable': False}
INFO:root:Requesting new tokens from https://*************/api/fmc_platform/v1/auth/generatetoken.
INFO:root:Building base to URLs.
INFO:root:Populating vdbVersion, sruVersion, serverVersion, and geoVersion FMC instance variables.
INFO:root:This FMC's version is 7.4.2 (build 172)
INFO:root:GET success. Object with name: "RadSaratoga" and id: "AC2AA168-21DA-0ed3-0000-004294970384" fetched from FMC.
INFO:root:Auto deploy changes set to False.  Use the Deploy button in FMC to push changes to FTDs.
DEBUG:fmc_migration_v2:GET result for RadSaratoga: success=False, message='fmcapi get() returned no data for 'RadSaratoga''
INFO:root:Requesting new tokens from https://*************/api/fmc_platform/v1/auth/generatetoken.
INFO:root:Building base to URLs.
INFO:root:Populating vdbVersion, sruVersion, serverVersion, and geoVersion FMC instance variables.
INFO:root:This FMC's version is 7.4.2 (build 172)
ERROR:root:Error in POST operation --> 400 Client Error: 400 for url: https://*************/api/fmc_config/v1/domain/e276abec-e0f2-11e3-8169-6d9ed49b625f/object/hosts
ERROR:root:json_response -->    {'error': {'category': 'FRAMEWORK', 'messages': [{'description': 'The object name RadSaratoga already exists. Enter a new name.'}], 'severity': 'ERROR'}}
WARNING:root:POST failure.  No data in API response.
INFO:root:Auto deploy changes set to False.  Use the Deploy button in FMC to push changes to FTDs.
DEBUG:fmc_migration_v2:POST result for RadSaratoga: success=False, message='fmcapi post() failed - no ID returned. Result: None'
2025-08-04 05:33:48,307 | ERROR | Creation failed for RadSaratoga: fmcapi post() failed - no ID returned. Result: None
ERROR:fmc_migration_v2:Creation failed for RadSaratoga: fmcapi post() failed - no ID returned. Result: None
DEBUG:fmc_migration_v2:Processing hosts 2: RadAmsMem
DEBUG:fmc_migration_v2:Object data keys: ['name', 'type', 'value', 'description', 'overridable']
DEBUG:fmc_migration_v2:Object data: {'name': 'RadAmsMem', 'type': 'Host', 'value': '***********', 'description': 'Migrated from ASA - ', 'overridable': False}
INFO:root:Requesting new tokens from https://*************/api/fmc_platform/v1/auth/generatetoken.
INFO:root:Building base to URLs.
INFO:root:Populating vdbVersion, sruVersion, serverVersion, and geoVersion FMC instance variables.
INFO:root:This FMC's version is 7.4.2 (build 172)
INFO:root:GET success. Object with name: "RadAmsMem" and id: "AC2AA168-21DA-0ed3-0000-004294970348" fetched from FMC.
INFO:root:Auto deploy changes set to False.  Use the Deploy button in FMC to push changes to FTDs.
DEBUG:fmc_migration_v2:GET result for RadAmsMem: success=False, message='fmcapi get() returned no data for 'RadAmsMem''
INFO:root:Requesting new tokens from https://*************/api/fmc_platform/v1/auth/generatetoken.
INFO:root:Building base to URLs.
INFO:root:Populating vdbVersion, sruVersion, serverVersion, and geoVersion FMC instance variables.
INFO:root:This FMC's version is 7.4.2 (build 172)
ERROR:root:Error in POST operation --> 400 Client Error: 400 for url: https://*************/api/fmc_config/v1/domain/e276abec-e0f2-11e3-8169-6d9ed49b625f/object/hosts
ERROR:root:json_response -->    {'error': {'category': 'FRAMEWORK', 'messages': [{'description': 'The object name RadAmsMem already exists. Enter a new name.'}], 'severity': 'ERROR'}}
WARNING:root:POST failure.  No data in API response.
INFO:root:Auto deploy changes set to False.  Use the Deploy button in FMC to push changes to FTDs.
DEBUG:fmc_migration_v2:POST result for RadAmsMem: success=False, message='fmcapi post() failed - no ID returned. Result: None'
2025-08-04 05:33:52,428 | ERROR | Creation failed for RadAmsMem: fmcapi post() failed - no ID returned. Result: None
ERROR:fmc_migration_v2:Creation failed for RadAmsMem: fmcapi post() failed - no ID returned. Result: None
DEBUG:fmc_migration_v2:Processing hosts 3: RadStMarys
DEBUG:fmc_migration_v2:Object data keys: ['name', 'type', 'value', 'description', 'overridable']
DEBUG:fmc_migration_v2:Object data: {'name': 'RadStMarys', 'type': 'Host', 'value': '***********', 'description': 'Migrated from ASA - ', 'overridable': False}
INFO:root:Requesting new tokens from https://*************/api/fmc_platform/v1/auth/generatetoken.

[STOP] Migration interrupted by user