#!/usr/bin/env python3
"""
Migration Coverage Verification Script

This script analyzes the migration configuration file to verify that all objects
will be properly synchronized by the enhanced migration script.
"""

import json
import sys
from typing import Dict, List, Any

def analyze_config_file(config_file: str) -> Dict[str, Any]:
    """Analyze the migration configuration file"""
    
    print(f"🔍 Analyzing migration configuration: {config_file}")
    print("=" * 80)
    
    try:
        with open(config_file, 'r') as f:
            config = json.load(f)
    except Exception as e:
        print(f"❌ Error loading configuration file: {e}")
        return {}
    
    analysis = {
        'total_objects': 0,
        'object_types': {},
        'coverage_status': {},
        'missing_types': []
    }
    
    # Check metadata if available
    if 'metadata' in config and 'total_objects' in config['metadata']:
        print("📊 METADATA SUMMARY:")
        metadata = config['metadata']['total_objects']
        for obj_type, count in metadata.items():
            print(f"   • {obj_type}: {count}")
        print()
    
    # Analyze api_calls structure
    if 'api_calls' in config:
        print("📋 API CALLS ANALYSIS:")
        api_calls = config['api_calls']
        
        for section_name, section_data in api_calls.items():
            if isinstance(section_data, dict) and 'data' in section_data:
                count = len(section_data['data'])
                endpoint = section_data.get('endpoint', 'Unknown')
                method = section_data.get('method', 'Unknown')
                
                analysis['object_types'][section_name] = {
                    'count': count,
                    'endpoint': endpoint,
                    'method': method,
                    'supported': True
                }
                analysis['total_objects'] += count
                
                print(f"   ✅ {section_name}: {count} objects")
                print(f"      Endpoint: {endpoint}")
                print(f"      Method: {method}")
                
                # Show sample object if available
                if count > 0 and section_data['data']:
                    sample = section_data['data'][0]
                    sample_name = sample.get('name', 'Unknown')
                    sample_type = sample.get('type', 'Unknown')
                    print(f"      Sample: {sample_name} (type: {sample_type})")
                print()
    
    # Check for direct object sections (v2.0 format)
    direct_sections = ['host_objects', 'network_objects', 'service_objects', 'object_groups', 'service_groups']
    for section in direct_sections:
        if section in config and section not in analysis['object_types']:
            if isinstance(config[section], list):
                count = len(config[section])
                analysis['object_types'][section] = {
                    'count': count,
                    'endpoint': 'Direct format',
                    'method': 'POST',
                    'supported': True
                }
                analysis['total_objects'] += count
                print(f"   ✅ {section} (direct): {count} objects")
    
    return analysis

def verify_migration_support(analysis: Dict[str, Any]) -> None:
    """Verify that the migration script supports all object types"""
    
    print("🔧 MIGRATION SCRIPT SUPPORT VERIFICATION:")
    print("=" * 80)
    
    # Define what the enhanced migration script supports
    supported_types = {
        'host_objects': 'HostObject class - Phase 1',
        'network_objects': 'NetworkObject class - Phase 2', 
        'service_objects': 'ProtocolPortObject class - Phase 3',
        'object_groups': 'NetworkGroup class - Phase 4',
        'service_groups': 'PortObjectGroup class - Phase 5'
    }
    
    all_supported = True
    
    for obj_type, details in analysis['object_types'].items():
        if obj_type in supported_types:
            print(f"   ✅ {obj_type}: SUPPORTED ({supported_types[obj_type]})")
            print(f"      Will migrate {details['count']} objects")
        else:
            print(f"   ❌ {obj_type}: NOT SUPPORTED")
            print(f"      {details['count']} objects will be SKIPPED")
            all_supported = False
        print()
    
    if all_supported:
        print("🎉 RESULT: ALL object types are supported by the enhanced migration script!")
        print(f"📊 Total objects to be migrated: {analysis['total_objects']}")
    else:
        print("⚠️  RESULT: Some object types are not supported and will be skipped.")
    
    print()

def main():
    """Main function"""
    if len(sys.argv) < 2:
        print("Usage: python verify_migration_coverage.py <migration_config.json>")
        sys.exit(1)
    
    config_file = sys.argv[1]
    
    # Analyze the configuration file
    analysis = analyze_config_file(config_file)
    
    if not analysis:
        sys.exit(1)
    
    # Verify migration script support
    verify_migration_support(analysis)
    
    # Summary
    print("📋 MIGRATION PHASES SUMMARY:")
    print("=" * 80)
    print("The enhanced migration script will execute these phases:")
    print("   Phase 1: Host Objects")
    print("   Phase 2: Network Objects") 
    print("   Phase 3: Service Objects")
    print("   Phase 4: Network Groups (Object Groups)")
    print("   Phase 5: Service Groups (Port Object Groups)")
    print()
    print("Each phase includes:")
    print("   • Object existence checking")
    print("   • Create or update operations")
    print("   • Error handling and retry logic")
    print("   • Progress tracking and logging")
    print("   • Checkpoint creation for resume capability")
    print()
    print("🚀 Ready for complete synchronization!")

if __name__ == "__main__":
    main()
